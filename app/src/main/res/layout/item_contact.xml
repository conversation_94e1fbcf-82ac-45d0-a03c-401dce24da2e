<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <CheckBox
        android:id="@+id/contactCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:buttonTint="@color/colorPrimary" />

    <TextView
        android:id="@+id/contactName"
        android:layout_width="0dp"
        android:layout_gravity="center_vertical"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingStart="8dp"
        android:text="Contact Name sdfksldfsdkf" />

    <TextView
        android:id="@+id/contactPhone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Phone Number" />
</LinearLayout>
