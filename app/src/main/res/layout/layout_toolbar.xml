<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/toolbar_top"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:outlineProvider="none"
    app:cardCornerRadius="0dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="#ffffff">

        <ImageButton
            android:id="@+id/back_img_btn"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="?selectableItemBackgroundBorderless"
            android:src="@drawable/back_btn_svg"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/toolbar_title"
            app:layout_constraintStart_toStartOf="@id/start"
            app:layout_constraintTop_toTopOf="@+id/toolbar_title" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#000000"
            android:textSize="19sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Title" />


        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent=".04" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>


