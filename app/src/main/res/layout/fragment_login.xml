<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".LoginFragment">

    <include
        android:id="@+id/layoutToolbar"
        layout="@layout/layout_toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/emailTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:text="@string/email"
        android:textColor="#272F3D"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutToolbar" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/emailInputText"
        style="@style/OutlinedRoundedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="20dp"
        app:boxStrokeColor="@color/box_stroke_selector"
        app:boxStrokeErrorColor="@color/red"
        app:endIconMode="clear_text"
        app:errorEnabled="true"
        app:helperTextEnabled="true"
        app:helperTextTextColor="@color/red"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emailTextView">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/email"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:hint="@string/enter_your_email_hint"
            android:inputType="textEmailAddress"
            android:paddingVertical="2dp"
            android:text=""
            android:textColor="#272F3D"
            android:textColorHint="@color/grey_D1D5DB"
            android:textSize="13dp" />
    </com.google.android.material.textfield.TextInputLayout>


    <TextView
        android:id="@+id/passwordTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:text="@string/password"
        android:textColor="#272F3D"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emailInputText" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/passwordInputText"
        style="@style/OutlinedRoundedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="20dp"
        app:boxStrokeColor="@color/box_stroke_selector"
        app:boxStrokeErrorColor="@color/red"
        app:endIconMode="password_toggle"
        app:errorEnabled="true"
        app:helperTextEnabled="true"
        app:helperTextTextColor="@color/red"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/passwordTextView"
        app:passwordToggleDrawable="@drawable/show_password_selector"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/password"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:hint="@string/lbl_enter_password"
            android:inputType="textPassword"
            android:paddingVertical="2dp"
            android:text=""
            android:textColor="#272F3D"
            android:textColorHint="@color/grey_D1D5DB"
            android:textSize="13dp" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/checkBox"
        style="@style/Widget.App.CheckBox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/checked_unchecked_selector"
        android:checked="true"
        android:text="@string/remember_email_checkbox_text"
        android:textColor="@color/grey_text_color"
        app:buttonTint="@color/logo_color"
        app:layout_constraintStart_toStartOf="@+id/passwordInputText"
        app:layout_constraintTop_toBottomOf="@+id/passwordInputText" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:indeterminateTint="@color/color_button_3F6FC3"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/loginButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/passwordInputText" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/loginButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/round"
        android:text="@string/login"
        android:textAllCaps="false"
        android:textColor="#FFFFFF"
        app:backgroundTint="@color/selected_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/checkBox" />


    <TextView
        android:id="@+id/errorTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="@color/red"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/loginButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/checkBox" />

</androidx.constraintlayout.widget.ConstraintLayout>