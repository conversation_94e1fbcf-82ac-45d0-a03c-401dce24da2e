<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/bottomBar"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/bottom_sheet_top_bar" />


    <ImageView
        android:id="@+id/crossButton"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:paddingEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bottomBar"
        android:src="@drawable/baseline_close_24" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/horizontalRecyclerView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/crossButton" />
</androidx.constraintlayout.widget.ConstraintLayout>