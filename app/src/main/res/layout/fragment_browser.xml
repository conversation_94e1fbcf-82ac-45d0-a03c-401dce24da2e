<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:focusableInTouchMode="true">

    </WebView>


    <ImageView
        android:id="@+id/loader"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:padding="50dp" />

 <!--   <Button
        android:id="@+id/refreshButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:backgroundTint="#0324DD"
        android:drawableStart="@drawable/baseline_refresh_24"
        android:focusable="true"
        android:text="START OVER"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="40dp" />-->
</RelativeLayout>