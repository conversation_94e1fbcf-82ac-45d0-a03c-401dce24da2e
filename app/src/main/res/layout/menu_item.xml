<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:id="@+id/parent"
    android:background="@drawable/rounded_background"
    android:orientation="vertical"
    android:padding="8dp">

    <ImageView
        android:id="@+id/itemImage"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/itemText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingTop="4dp"
        android:text=""
        android:textColor="@android:color/black"
        android:textSize="14sp" />
</LinearLayout>