<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".NewContactFragment">

    <include
        android:id="@+id/layoutToolbar"
        layout="@layout/layout_toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/nameTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="20dp"
        android:text="Enter Name"
        android:textColor="#272F3D"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutToolbar" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/nameInputText"
        style="@style/OutlinedRoundedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="20dp"
        app:boxStrokeColor="@color/box_stroke_selector"
        app:boxStrokeErrorColor="@color/red"
        app:endIconMode="clear_text"
        app:errorEnabled="true"
        app:helperTextEnabled="true"
        app:helperTextTextColor="@color/red"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nameTextView">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:hint="Afrothentik"
            android:inputType="text"
            android:paddingVertical="2dp"
            android:text=""
            android:textColor="#272F3D"
            android:textColorHint="@color/grey_D1D5DB"
            android:textSize="13dp" />
    </com.google.android.material.textfield.TextInputLayout>


    <TextView
        android:id="@+id/phoneNumberText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:text="Enter Phone number"
        android:textColor="#272F3D"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nameInputText" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/numberInputText"
        style="@style/OutlinedRoundedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="20dp"
        app:boxStrokeColor="@color/box_stroke_selector"
        app:boxStrokeErrorColor="@color/red"
        app:errorEnabled="true"
        app:helperTextEnabled="true"
        app:helperTextTextColor="@color/red"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/phoneNumberText">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/number"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:hint="123456789"
            android:inputType="number"
            android:paddingVertical="2dp"
            android:text=""
            android:textColor="#272F3D"
            android:textColorHint="@color/grey_D1D5DB"
            android:textSize="13dp" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/birthdayText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:text="Enter Birthday"
        android:textColor="#272F3D"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/numberInputText" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/birthdayInputText"
        style="@style/OutlinedRoundedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="20dp"
        app:boxStrokeColor="@color/box_stroke_selector"
        app:boxStrokeErrorColor="@color/red"
        app:errorEnabled="true"
        app:helperTextEnabled="true"
        app:helperTextTextColor="@color/red"
        app:hintEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/birthdayText">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/date"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:clickable="true"
            android:drawableEnd="@drawable/arrow_down_grey"
            android:focusable="false"
            android:hint="Select your birthday"
            android:inputType="date"
            android:paddingVertical="2dp"
            android:textColor="#272F3D"
            android:textColorHint="@color/grey_D1D5DB"
            android:textSize="13dp" />
    </com.google.android.material.textfield.TextInputLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="20dp"
        android:indeterminateTint="@color/color_button_3F6FC3"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/numberInputText" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/saveContactButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/round"
        android:text="Save Contact"
        android:textAllCaps="false"
        android:textColor="#FFFFFF"
        app:backgroundTint="@color/selected_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/birthdayInputText" />

</androidx.constraintlayout.widget.ConstraintLayout>