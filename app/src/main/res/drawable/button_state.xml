<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- State when the button is focused -->
    <item android:drawable="@drawable/rounded_background_pressed" android:state_focused="true" />
    <!-- State when the button is pressed -->
    <item android:drawable="@drawable/rounded_background_pressed" android:state_pressed="true" />
    <!-- Default state -->
    <item android:drawable="@drawable/rounded_background" />
</selector>