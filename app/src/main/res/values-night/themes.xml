<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.AfrothentikTV" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/white</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
    </style>


    <style name="OutlinedRoundedBox" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="shapeAppearanceOverlay">
            @style/ShapeAppearanceOverlay.MyApp.TextInputLayout.Rounded
        </item>
    </style>
    <style name="ShapeAppearanceOverlay.MyApp.TextInputLayout.Rounded" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>
    <style name="Widget.App.CheckBox" parent="Widget.MaterialComponents.CompoundButton.CheckBox">
        <item name="buttonTint">@color/button_tint</item>
    </style>
</resources>