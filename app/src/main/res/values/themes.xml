<resources>

    <style name="Theme.AfrothentikTV" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/search_opaque</item>
        <item name="colorPrimaryVariant">@color/white</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
    </style>


    <style name="OutlinedRoundedBox" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="shapeAppearanceOverlay">
            @style/ShapeAppearanceOverlay.MyApp.TextInputLayout.Rounded
        </item>
    </style>
    <style name="ShapeAppearanceOverlay.MyApp.TextInputLayout.Rounded" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>
    <style name="Widget.App.CheckBox" parent="Widget.MaterialComponents.CompoundButton.CheckBox">
        <item name="buttonTint">@color/button_tint</item>
    </style>

    <style name="ThemeOverlay.App.DatePicker" parent="@style/ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="colorPrimary">@color/grey</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="shapeAppearanceSmallComponent">@color/red</item>
        <item name="shapeAppearanceMediumComponent">@color/grey</item>
    </style>
</resources>