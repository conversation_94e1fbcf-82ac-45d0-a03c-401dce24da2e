<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation.xml"
    app:startDestination="@id/browserFragment">

    <fragment
        android:id="@+id/browserFragment"
        android:name="com.basindev.afrothentiktv.BrowserFragment"
        android:label="BrowserFragment"
        tools:layout="@layout/fragment_browser" />
    <fragment
        android:id="@+id/wishlistFragment"
        android:name="com.basindev.afrothentiktv.WishlistFragment"
        android:label="fragment_wishlist"
        tools:layout="@layout/fragment_wishlist" />
    <fragment
        android:id="@+id/cartFragment"
        android:name="com.basindev.afrothentiktv.CartFragment"
        android:label="fragment_cart"
        tools:layout="@layout/fragment_cart" />
    <fragment
        android:id="@+id/menuFragment2"
        android:name="com.basindev.afrothentiktv.MenuFragment"
        android:label="fragment_menu"
        tools:layout="@layout/fragment_menu">
        <action
            android:id="@+id/action_menuFragment2_to_loginFragment"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/action_menuFragment2_to_newContactFragment"
            app:destination="@id/newContactFragment" />
        <action
            android:id="@+id/action_menuFragment2_to_importContactsFragment"
            app:destination="@id/importContactsFragment" />
        <action
            android:id="@+id/action_menuFragment2_to_importStaffFragment"
            app:destination="@id/importStaffFragment" />
    </fragment>
    <fragment
        android:id="@+id/loginFragment"
        android:name="com.basindev.afrothentiktv.LoginFragment"
        android:label="fragment_login"
        tools:layout="@layout/fragment_login" />
    <fragment
        android:id="@+id/newContactFragment"
        android:name="com.basindev.afrothentiktv.NewContactFragment"
        android:label="fragment_new_contact"
        tools:layout="@layout/fragment_new_contact">
        <action
            android:id="@+id/action_newContactFragment_to_loginFragment"
            app:destination="@id/loginFragment" />
        <argument
            android:name="from"
            app:argType="string" />
    </fragment>
    <fragment
        android:id="@+id/importContactsFragment"
        android:name="com.basindev.afrothentiktv.ImportContactsFragment"
        android:label="fragment_import_contacts"
        tools:layout="@layout/fragment_import_contacts">
        <action
            android:id="@+id/action_importContactsFragment_to_loginFragment"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/action_importContactsFragment_to_newContactFragment"
            app:destination="@id/newContactFragment">
            <argument
                android:name="from"
                app:argType="string" />
        </action>
    </fragment>
    <fragment
        android:id="@+id/myAccountFragment"
        android:name="com.basindev.afrothentiktv.MyAccountFragment"
        android:label="MyAccountFragment" />
    <fragment
        android:id="@+id/genericLinkOpeningFragment"
        android:name="com.basindev.afrothentiktv.GenericLinkOpeningFragment"
        android:label="fragment_generic_link_opening"
        tools:layout="@layout/fragment_generic_link_opening">
        <argument
            android:name="endpoint"
            app:argType="string" />
    </fragment>
    <dialog
        android:id="@+id/menuBottomSheet"
        android:name="com.basindev.afrothentiktv.MenuBottomSheet"
        android:label="MenuBottomSheet" >
        <action
            android:id="@+id/action_menuBottomSheet_to_genericLinkOpeningFragment"
            app:destination="@id/genericLinkOpeningFragment" >
            <argument
                android:name="endpoint"
                app:argType="string" />
        </action>
    </dialog>
    <fragment
        android:id="@+id/importStaffFragment"
        android:name="com.basindev.afrothentiktv.ImportStaffFragment"
        android:label="fragment_import_staff"
        tools:layout="@layout/fragment_import_staff" >
        <action
            android:id="@+id/action_importStaffFragment_to_newContactFragment"
            app:destination="@id/newContactFragment">
         <argument
            android:name="from"
            app:argType="string" />
        </action>
        <action
            android:id="@+id/action_importStaffFragment_to_loginFragment"
            app:destination="@id/loginFragment" />
    </fragment>
</navigation>