package com.basindev.afrothentiktv

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView

/**
 * Loads [MainFragment].
 */
class MainActivity : FragmentActivity() {
    private lateinit var navHostFragment: NavHostFragment
    private lateinit var navController: NavController
    private var isFullScreen = false
    private lateinit var toggleButton: Button
    private lateinit var fragmentContainerYouTube: FrameLayout
    private lateinit var fragmentContainerWeb: FrameLayout
    private lateinit var mainWebBrowserConstraintLayout: ConstraintLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val screenSize =
            resources.configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK
        val isLargeScreen = screenSize >= Configuration.SCREENLAYOUT_SIZE_LARGE
        setContentView(R.layout.activity_main)
        val bottomNav = findViewById<BottomNavigationView>(R.id.bottom_navigation)
        val navView: BottomNavigationView = bottomNav
        if (!isRunningOnTV(this)) {
            bottomNav.visibility = View.VISIBLE
            navHostFragment =
                supportFragmentManager.findFragmentById(R.id.fragmentContainerView) as NavHostFragment
            navController = navHostFragment.navController
            navView.setupWithNavController(navController)

            navController.addOnDestinationChangedListener { _, destination, _ ->
                if (destination.id == R.id.newContactFragment) {
                    bottomNav.visibility = View.GONE
                } else {
                    bottomNav.visibility = View.VISIBLE
                }
            }
        } else {
            bottomNav.visibility = View.GONE
        }

        //for tv
     /*   if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragmentContainerWeb, BrowserFragment())
                .commitNow()
        }*/
    }

    override fun onBackPressed() {
        val fragment = navHostFragment.childFragmentManager.fragments[0]
        when (fragment) {
            is BrowserFragment -> {
                val handled = fragment.onBackPressed()
                if (!handled) {
                    super.onBackPressed() // exit app or go back
                }
            }
            is WishlistFragment -> {
                val handled = fragment.onBackPressed()
                if (!handled) {
                    super.onBackPressed() // exit app or go back
                }
            }
            is CartFragment -> {
                val handled = fragment.onBackPressed()
                if (!handled) {
                    super.onBackPressed() // exit app or go back
                }
            }
            is MyAccountFragment -> {
                val handled = fragment.onBackPressed()
                if (!handled) {
                    super.onBackPressed() // exit app or go back
                }
            }

            is GenericLinkOpeningFragment -> {
                val handled = fragment.onBackPressed()
                if (!handled) {
                    super.onBackPressed() // exit app or go back
                }
            }
            else -> {
                navController.popBackStack()
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        // Handle the new intent if needed
    }

    private fun isRunningOnTV(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_LEANBACK) ||
                context.packageManager.hasSystemFeature(PackageManager.FEATURE_TELEVISION)
    }
}