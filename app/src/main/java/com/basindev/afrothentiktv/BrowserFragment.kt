package com.basindev.afrothentiktv

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.ImageView
import androidx.fragment.app.Fragment
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.show
import com.bumptech.glide.Glide
import com.google.firebase.firestore.FirebaseFirestore

class BrowserFragment : Fragment() {
    // val webpageUrl = "https://www.afrothentik.com/mobile-slides/"
    private val db = FirebaseFirestore.getInstance()
    private lateinit var webView: WebView
    private lateinit var refreshButton: Button
    private lateinit var loader: ImageView
    private var longPressDetected = false
    var deviceId = ""
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_browser, container, false)
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        webView = view.findViewById(R.id.webView)
        webView.canGoBack()
        loader = view.findViewById(R.id.loader)
      //  refreshButton = view.findViewById(R.id.refreshButton)

        // Enable JavaScript
        val webSettings: WebSettings = webView.settings
        webSettings.javaScriptEnabled = true
        deviceId = Settings.Secure.getString(
            requireContext().contentResolver,
            Settings.Secure.ANDROID_ID
        )
        showLoader()
        // Load the preloaded website
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideLoader()
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                hideLoader()
            }
        }
        if (!isRunningOnTVFrag(requireContext())) {
            webView.loadUrl("https://www.afrothentik.com")
        } else {
            getUrlFromFirebase()
        }

        //for tv
     /*   refreshButton.setOnClickListener {
            val intent = requireActivity().intent
            requireActivity().finish() // Close the current activity
            startActivity(intent)
        }*/

        //   www.afrothentik.com
    }
    fun onBackPressed(): Boolean {
        return if (webView.canGoBack()) {
            webView.goBack()
            true
        } else {
            false
        }
    }


    private fun showLoader() {
        loader.show()
        Glide
            .with(requireContext())
            .load(R.raw.anim_logo)
            .centerCrop()
            .into(loader)
    }

    private fun hideLoader() {
        loader.hide()
    }

    private fun isRunningOnTVFrag(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_LEANBACK) ||
                context.packageManager.hasSystemFeature(PackageManager.FEATURE_TELEVISION)
    }

    private fun getUrlFromFirebase() {
        val collectionName = "webpageurl"
        val documentId = "webpage"
        val keyName = "url"

        val docRef = db.collection(collectionName).document(documentId)

        docRef.get().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val document = task.result
                if (document.exists()) {
                    val url = document.getString(keyName)
                    //   if (isRunningOnTV(requireContext())) {
                    url?.let {
                        webView.loadUrl("${it}?device_id=${deviceId}")
                    }
                    /*  } else {
                          url?.let {
                              openChromeOrDefaultBrowser(requireContext(), it)
                          }
                      }*/
                } else {
                    Log.d("TAG", "No such document")
                }
            } else {
                Log.d("TAG", "get failed with ", task.exception)
            }
        }
    }

    private fun isRunningOnTV(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_LEANBACK) ||
                context.packageManager.hasSystemFeature(PackageManager.FEATURE_TELEVISION)
    }

    override fun onResume() {
        super.onResume()
        view?.isFocusableInTouchMode = true
        view?.requestFocus()

        view?.setOnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_DPAD_CENTER || keyCode == KeyEvent.KEYCODE_ENTER) {
                when (event.action) {
                    KeyEvent.ACTION_DOWN -> {
                        longPressDetected = true
                        // Delay to detect long press
                        v.postDelayed({
                            if (longPressDetected) {
                                webView.reload() // Reload WebView on long press
                            }
                        }, 100) // Adjust the delay as per your needs
                        true
                    }

                    KeyEvent.ACTION_UP -> {
                        longPressDetected = false
                        true
                    }

                    else -> false
                }
            } else {
                false
            }
        }
    }

    private fun openChromeOrDefaultBrowser(context: Context, url: String) {
        if (!isRunningOnTV(context)) {
            try {
                val uri = Uri.parse(url)
                val intent = Intent(Intent.ACTION_VIEW, uri)

                // Check if Chrome is installed
                intent.setPackage("com.android.chrome")
                if (intent.resolveActivity(context.packageManager) != null) {
                    context.startActivity(intent)
                } else {
                    // Open with the default browser
                    intent.setPackage(null)
                    context.startActivity(intent)
                }
            } catch (e: Exception) {
                Log.e("BrowserError", "Unable to open browser", e)
            }
        }
    }
}