package com.basindev.afrothentiktv

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.basindev.afrothentiktv.data.RetrofitInstance.apiService
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.show
import com.basindev.afrothentiktv.extensions.showToast
import com.basindev.afrothentiktv.model.Contact
import com.basindev.afrothentiktv.model.SaveContactsRequest
import com.basindev.afrothentiktv.model.SaveContactsResponse
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import retrofit2.Call
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

class NewContactFragment : Fragment() {
    val LOGIN_PREF_KEY = "LoginPrefs"
    val entity_id = "entity_id"
    val user_id = "user_id"
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var toolbarTitle: TextView
    private lateinit var back_img_btn: ImageButton
    private lateinit var saveContactButton: Button
    private lateinit var name: TextInputEditText
    private lateinit var nameInputText: TextInputLayout
    private lateinit var number: TextInputEditText
    private lateinit var date: TextInputEditText
    private lateinit var numberInputText: TextInputLayout
    private lateinit var progressBar: ProgressBar
    var from = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_new_contact, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        from = arguments?.getString("from") ?: ""
        sharedPreferences =
            requireContext().getSharedPreferences(LOGIN_PREF_KEY, Context.MODE_PRIVATE)
        val toolbarLayout: View = view.findViewById(R.id.layoutToolbar)
        toolbarTitle = toolbarLayout.findViewById(R.id.toolbar_title)
        back_img_btn = toolbarLayout.findViewById(R.id.back_img_btn)
        saveContactButton = view.findViewById(R.id.saveContactButton)
        progressBar = view.findViewById(R.id.progressBar)
        name = view.findViewById(R.id.name)
        number = view.findViewById(R.id.number)
        date = view.findViewById(R.id.date)
        nameInputText = view.findViewById(R.id.nameInputText)
        numberInputText = view.findViewById(R.id.numberInputText)
        toolbarTitle.text = "New Contact"
        val entityId = sharedPreferences.getString(entity_id, "") ?: ""
        val userId = sharedPreferences.getString(user_id, "") ?: ""
        if (entityId.isNullOrEmpty() && userId.isNullOrEmpty()) {
            findNavController().navigate(R.id.action_newContactFragment_to_loginFragment)
        }
        back_img_btn.setOnClickListener {
            findNavController().popBackStack()
        }

        date.setOnClickListener {
            showDatePicker()
        }

        saveContactButton.setOnClickListener {
            if (entityId.isNullOrEmpty() && userId.isNullOrEmpty()) {
                findNavController().navigate(R.id.action_newContactFragment_to_loginFragment)
            } else {
                if (!name.text.isNullOrEmpty()) {
                    if (number.text.isNullOrEmpty()) {
                        numberInputText.error = "Please Enter Number"
                    } else {
                        val request = SaveContactsRequest(
                            entity_id = entityId,
                            user_id = userId,
                            data = listOf(
                                Contact(
                                    contact_name = name.text.toString(),
                                    contact_phone = number.text.toString(),
                                    selectedDate
                                )
                            )
                        )
                        saveContacts(request)
                    }
                } else {
                    nameInputText.error = "Please Enter Name"
                }
            }
        }
    }

    private fun saveContacts(savedContactRequest: SaveContactsRequest) {
        // Use the ApiService to send the POST request
        var call = apiService.saveContacts(savedContactRequest)
        call = if (from == "Staff") {
            apiService.saveStaff(savedContactRequest)
        } else {
            apiService.saveContacts(savedContactRequest)
        }

        progressBar.show()
        call.enqueue(object : retrofit2.Callback<SaveContactsResponse> {
            override fun onResponse(
                call: Call<SaveContactsResponse>,
                response: Response<SaveContactsResponse>
            ) {
                if (response.isSuccessful) {
                    progressBar.hide()
                    // Handle successful response
                    val responseData = response.body()
                    showToast("Successful")
                    number.setText("")
                    name.setText("")
                } else {
                    progressBar.hide()
                    // Handle error response
                    showToast("Error occured")
                }
            }

            override fun onFailure(call: Call<SaveContactsResponse>, t: Throwable) {
                // Handle request failure
                progressBar.hide()
                showToast("Error occured")
            }
        })
    }

    var selectionDates: Long = 0
    var selectedDate = ""
    private fun showDatePicker() {
        if (selectionDates == 0.toLong()) {
            selectionDates = getDateInMillis(1990, 0, 1)
            //selectionDates = MaterialDatePicker.todayInUtcMilliseconds()
        }
        val constraintsBuilder =
            CalendarConstraints.Builder()
                .setValidator(DateValidatorPointBackward.now()).build()
        val datePicker =
            MaterialDatePicker.Builder.datePicker()
                .setTitleText("Select date")
                .setCalendarConstraints(constraintsBuilder)
                .setTheme(R.style.ThemeOverlay_App_DatePicker)
                .setSelection(selectionDates)
                .build()
        datePicker.show(parentFragmentManager, "datePicker")
        datePicker.addOnPositiveButtonClickListener {
            val sdfToShow = SimpleDateFormat("dd MMM, yyyy", Locale.getDefault())
            val sdfToSend = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'['z']'", Locale.US)
            val dateToShow = sdfToShow.format(it)
            date.setText(dateToShow)
            selectedDate = sdfToSend.toString()
            selectionDates = it
        }
    }

    private fun getDateInMillis(year: Int, month: Int, day: Int): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.YEAR, year)
        calendar.set(Calendar.MONTH, month)
        calendar.set(Calendar.DAY_OF_MONTH, day)
        return calendar.timeInMillis
    }


}