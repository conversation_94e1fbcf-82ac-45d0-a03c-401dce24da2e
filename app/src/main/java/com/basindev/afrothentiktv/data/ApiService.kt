package com.basindev.afrothentiktv.data

import com.basindev.afrothentiktv.model.EntityIdResponse
import com.basindev.afrothentiktv.model.LoginRequest
import com.basindev.afrothentiktv.model.LoginResponse
import com.basindev.afrothentiktv.model.SaveContactsRequest
import com.basindev.afrothentiktv.model.SaveContactsResponse
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

interface ApiService {

    @POST("v1/api/user_login")
    fun login(@Body request: LoginRequest): Call<LoginResponse>

    @POST("v1/api/save_contacts")
    fun saveContacts(@Body request: SaveContactsRequest): Call<SaveContactsResponse>


    @POST("v1/api/save_contacts?isStaff=true")
    fun saveStaff(@Body request: SaveContactsRequest): Call<SaveContactsResponse>


    @POST("z_api/get_entity")
    fun getEntity(): Call<EntityIdResponse>
}
