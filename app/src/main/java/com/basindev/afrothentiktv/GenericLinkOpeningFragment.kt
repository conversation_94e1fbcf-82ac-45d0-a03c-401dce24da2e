package com.basindev.afrothentiktv

import android.Manifest
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.basindev.afrothentiktv.extensions.getDeviceIpAddress
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.show
import com.bumptech.glide.Glide
import com.google.firebase.firestore.FirebaseFirestore

class GenericLinkOpeningFragment : Fragment() {
    private val db = FirebaseFirestore.getInstance()
    private lateinit var progressBar: ProgressBar
    private lateinit var webView: WebView
    private var filePathCallback: ValueCallback<Array<Uri>>? = null
    private val REQUEST_SELECT_FILE = 100
    private val PERMISSION_REQUEST_CODE = 101
    val LOGIN_PREF_KEY = "LoginPrefs"
    val entity_id = "entity_id"
    val user_id = "user_id"
    private lateinit var loader: ImageView
    private lateinit var sharedPreferences: SharedPreferences
    private val fileChooserLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            val result = WebChromeClient.FileChooserParams.parseResult(it.resultCode, it.data)
            filePathCallback?.onReceiveValue(result)
            filePathCallback = null
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_generic_link_opening, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val endpoint = arguments?.getString("endpoint") ?: ""

        if (!hasStoragePermission()) {
            requestStoragePermission()
        }
        webView = view.findViewById(R.id.webView)
        webView.canGoBack()
        webView.settings.javaScriptEnabled = true
        webView.settings.allowFileAccess = true
        webView.settings.domStorageEnabled = true

        webView.webChromeClient = object : WebChromeClient() {
            override fun onShowFileChooser(
                view: WebView?,
                filePathCallback: ValueCallback<Array<Uri>>,
                fileChooserParams: FileChooserParams
            ): Boolean {
                <EMAIL>?.onReceiveValue(null)

                <EMAIL> = filePathCallback

                try {
                    val intent = fileChooserParams.createIntent()
                    fileChooserLauncher.launch(intent)
                } catch (e: ActivityNotFoundException) {
                    Toast.makeText(requireContext(), "Cannot open file chooser", Toast.LENGTH_SHORT)
                        .show()
                    <EMAIL> = null
                    return false
                }
                return true
            }
        }
        loader = view.findViewById(R.id.loader)
        progressBar = view.findViewById(R.id.progressBar)
        sharedPreferences =
            requireContext().getSharedPreferences(LOGIN_PREF_KEY, Context.MODE_PRIVATE)
        var entityId = sharedPreferences.getString(entity_id, "")
        var userId = sharedPreferences.getString(user_id, "")
        showLoader()
        // Load the preloaded website
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideLoader()
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                hideLoader()
            }
        }
        val deviceId = Settings.Secure.getString(
            requireContext().contentResolver,
            Settings.Secure.ANDROID_ID
        )
        //  getEntityId()
        // val url = "https://www.afrothentik.com/z_account/wishlist?device_id=${deviceId}&ip=${getDeviceIpAddress()}&entity_id=${entityId}&user_id=${userId}"
        val url =
            "https://www.afrothentik.com/z_account/${endpoint}?device_id=${deviceId}&ip=${getDeviceIpAddress()}"
        webView.loadUrl(url)
    }

    private fun showLoader() {
        loader.show()
        Glide
            .with(requireContext())
            .load(R.raw.anim_logo)
            .centerCrop()
            .into(loader)
    }

    fun onBackPressed(): Boolean {
        return if (webView.canGoBack()) {
            webView.goBack()
            true
        } else {
            false
        }
    }

    private fun hideLoader() {
        loader.hide()
    }

    private fun hasStoragePermission(): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.READ_MEDIA_IMAGES
                ) ==
                        PackageManager.PERMISSION_GRANTED
            }

            else -> {
                ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.READ_EXTERNAL_STORAGE
                ) ==
                        PackageManager.PERMISSION_GRANTED
            }
        }
    }

    private fun requestStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissions(
                arrayOf(Manifest.permission.READ_MEDIA_IMAGES),
                PERMISSION_REQUEST_CODE
            )
        } else {
            requestPermissions(
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                PERMISSION_REQUEST_CODE
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isEmpty() || grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(
                    requireContext(),
                    "Permission required to upload files",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

}