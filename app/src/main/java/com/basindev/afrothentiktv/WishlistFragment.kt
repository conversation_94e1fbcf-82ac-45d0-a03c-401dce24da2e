package com.basindev.afrothentiktv

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.fragment.app.Fragment
import com.basindev.afrothentiktv.data.RetrofitInstance
import com.basindev.afrothentiktv.extensions.getDeviceIpAddress
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.show
import com.basindev.afrothentiktv.extensions.showToast
import com.basindev.afrothentiktv.model.EntityIdResponse
import com.bumptech.glide.Glide
import com.google.firebase.firestore.FirebaseFirestore
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class WishlistFragment : Fragment() {
    private val db = FirebaseFirestore.getInstance()
    private lateinit var progressBar: ProgressBar
    private lateinit var webView: WebView
    val LOGIN_PREF_KEY = "LoginPrefs"
    val entity_id = "entity_id"
    val user_id = "user_id"
    private lateinit var loader: ImageView
    private lateinit var sharedPreferences: SharedPreferences
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_wishlist, container, false)
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        webView = view.findViewById(R.id.webView)
        webView.canGoBack()
        loader = view.findViewById(R.id.loader)
        progressBar = view.findViewById(R.id.progressBar)
        sharedPreferences =
            requireContext().getSharedPreferences(LOGIN_PREF_KEY, Context.MODE_PRIVATE)
        var entityId = sharedPreferences.getString(entity_id, "")
        var userId = sharedPreferences.getString(user_id, "")
        // Enable JavaScript
        val webSettings: WebSettings = webView.settings
        webSettings.javaScriptEnabled = true
        showLoader()
        // Load the preloaded website
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideLoader()
            }
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                hideLoader()
            }
        }
        val deviceId = Settings.Secure.getString(
            requireContext().contentResolver,
            Settings.Secure.ANDROID_ID
        )
      //  getEntityId()
       // val url = "https://www.afrothentik.com/z_account/wishlist?device_id=${deviceId}&ip=${getDeviceIpAddress()}&entity_id=${entityId}&user_id=${userId}"
        val url = "https://www.afrothentik.com/z_account/wishlist?device_id=${deviceId}&ip=${getDeviceIpAddress()}"
        webView.loadUrl(url)
    }

    fun onBackPressed(): Boolean {
        return if (webView.canGoBack()) {
            webView.goBack()
            true
        } else {
            false
        }
    }

    private fun showLoader(){
        loader.show()
        Glide
            .with(requireContext())
            .load(R.raw.anim_logo)
            .centerCrop()
            .into(loader)
    }

    private fun hideLoader(){
        loader.hide()
    }

/*    private fun getEntityId() {
        progressBar.show()
        RetrofitInstance.apiService.getEntity().enqueue(object : Callback<EntityIdResponse> {
            override fun onResponse(
                call: Call<EntityIdResponse>,
                response: Response<EntityIdResponse>
            ) {
                if (response.isSuccessful && response.body() != null) {
                    progressBar.hide()
                    val entityId = response.body()?.result?.entity_id
                    val userId = response.body()?.result?.user_id
                    if(entityId!=null && userId!=null){

                    }else{

                    }
                } else {
                    val url =
                        "https://www.afrothentik.com/z_account/wishlist"
                    webView.loadUrl(url)
                }
            }

            override fun onFailure(call: Call<EntityIdResponse>, t: Throwable) {
                progressBar.hide()
                showToast("Please try again!")
            }
        })
    }*/

    private fun getUrlFromFirebase() {
        val collectionName = "webpageurl"
        val documentId = "webpage"
        val keyName = "url"

        val docRef = db.collection(collectionName).document(documentId)

        docRef.get().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val document = task.result
                if (document.exists()) {
                    val url = document.getString(keyName) + "wishlist"
                    //   if (isRunningOnTV(requireContext())) {
                    url?.let {
                        webView.loadUrl(it)
                    }
                    /*  } else {
                          url?.let {
                              openChromeOrDefaultBrowser(requireContext(), it)
                          }
                      }*/
                } else {
                    Log.d("TAG", "No such document")
                }
            } else {
                Log.d("TAG", "get failed with ", task.exception)
            }
        }
    }

    private fun isRunningOnTV(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_LEANBACK) ||
                context.packageManager.hasSystemFeature(PackageManager.FEATURE_TELEVISION)
    }

}