package com.basindev.afrothentiktv

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.basindev.afrothentiktv.adapter.HorizontalAdapter
import com.basindev.afrothentiktv.model.BottomMenuItem
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class MenuBottomSheet : BottomSheetDialogFragment() {
    private lateinit var crossButton: ImageView
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_menu_bottom_sheet, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        crossButton = view.findViewById(R.id.crossButton)
        val recyclerView: RecyclerView = view.findViewById(R.id.horizontalRecyclerView)
        recyclerView.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        recyclerView.adapter = HorizontalAdapter(getItems()) {
            if (it.text.equals("Contacts")) {
                //findNavController().navigate(R.id.menuFragment2)
                findNavController().navigate(R.id.importContactsFragment)
                dismiss()
            } else if (it.text.equals("Staffs")) {
                findNavController().navigate(R.id.importStaffFragment)
                dismiss()
            } else if (it.text.equals("Hair")) {
                val nav =
                    MenuBottomSheetDirections.actionMenuBottomSheetToGenericLinkOpeningFragment("Hair")
                findNavController().navigate(nav)
                dismiss()
            } else if (it.text.equals("Event")) {
                val nav =
                    MenuBottomSheetDirections.actionMenuBottomSheetToGenericLinkOpeningFragment("Event")
                findNavController().navigate(nav)
                dismiss()

            } else if (it.text.equals("More")) {
                val nav =
                    MenuBottomSheetDirections.actionMenuBottomSheetToGenericLinkOpeningFragment("More")
                findNavController().navigate(nav)
                dismiss()

            }
        }
        crossButton.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun getItems(): List<BottomMenuItem> {
        return listOf(
            BottomMenuItem("Contacts", R.drawable.contacts),
            BottomMenuItem("Staffs", R.drawable.staff),
           // BottomMenuItem("Hair", R.drawable.hair),
            BottomMenuItem("Event", R.drawable.events),
            BottomMenuItem("More", R.drawable.more),
        )
    }
}