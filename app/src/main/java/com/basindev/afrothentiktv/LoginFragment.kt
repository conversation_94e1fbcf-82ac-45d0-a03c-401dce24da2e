package com.basindev.afrothentiktv

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.CheckBox
import android.widget.ImageButton
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.basindev.afrothentiktv.data.RetrofitInstance
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.isEmailInTextFieldValid
import com.basindev.afrothentiktv.extensions.show
import com.basindev.afrothentiktv.extensions.showToast
import com.basindev.afrothentiktv.model.LoginRequest
import com.basindev.afrothentiktv.model.LoginResponse
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class LoginFragment : Fragment() {
    val LOGIN_PREF_KEY = "LoginPrefs"
    val entity_id = "entity_id"
    val user_id = "user_id"
    private lateinit var loginButton: Button
    private lateinit var email: TextInputEditText
    private lateinit var back_img_btn: ImageButton
    private lateinit var emailInputText: TextInputLayout
    private lateinit var password: TextInputEditText
    private lateinit var passwordInputText: TextInputLayout
    private lateinit var progressBar: ProgressBar
    private lateinit var toolbarTitle: TextView
    private lateinit var rememberMeCheckBox: CheckBox
    private lateinit var sharedPreferences: SharedPreferences
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_login, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        sharedPreferences =
            requireContext().getSharedPreferences(LOGIN_PREF_KEY, Context.MODE_PRIVATE)
        loginButton = view.findViewById(R.id.loginButton)

        email = view.findViewById(R.id.email)
        emailInputText = view.findViewById(R.id.emailInputText)
        passwordInputText = view.findViewById(R.id.passwordInputText)
        password = view.findViewById(R.id.password)
        progressBar = view.findViewById(R.id.progressBar)
        rememberMeCheckBox = view.findViewById(R.id.checkBox)
        val toolbarLayout: View = view.findViewById(R.id.layoutToolbar)
        toolbarTitle = toolbarLayout.findViewById(R.id.toolbar_title)
        back_img_btn = toolbarLayout.findViewById(R.id.back_img_btn)
        toolbarTitle.text = "Login"
        loginButton.setOnClickListener {
            if (!email.text.isNullOrEmpty() && isEmailInTextFieldValid(email.text.toString())) {
                if (password.text.isNullOrEmpty()) {
                    passwordInputText.error = "Please Enter password"
                } else {
                    loginUser(email.text.toString(), password.text.toString())
                }
            } else {
                emailInputText.error = "Please Enter valid email!"
            }
        }
        back_img_btn.setOnClickListener {
            findNavController().popBackStack()
            findNavController().popBackStack()
        }
    }


    private fun loginUser(email: String, password: String) {
        val loginRequest = LoginRequest(email, password)
        progressBar.show()
        RetrofitInstance.apiService.login(loginRequest).enqueue(object : Callback<LoginResponse> {
            override fun onResponse(call: Call<LoginResponse>, response: Response<LoginResponse>) {
                if (response.isSuccessful && response.body() != null) {
                    progressBar.hide()
                    val loginResponse = response.body()
                    if (loginResponse?.result != null) {
                        if (rememberMeCheckBox.isChecked) {
                            val editor = sharedPreferences.edit()
                            editor.putString(entity_id, loginResponse?.result?.entity_id)
                            editor.putString(user_id, loginResponse?.result?.user_id)
                            editor.apply()
                            showToast("Logged in successfully!")
                            findNavController().popBackStack()
                        } else {
                            // Clear stored data if "Remember Me" is unchecked
                            // sharedPreferences.edit().clear().apply()
                            val editor = sharedPreferences.edit()
                            editor.putString(entity_id, loginResponse?.result?.entity_id)
                            editor.putString(user_id, loginResponse?.result?.user_id)
                            editor.apply()
                            showToast("Logged in successfully!")
                            findNavController().popBackStack()
                        }
                    } else {
                        showToast("Email or password invalid")
                    }
                } else {
                    // Handle error response
                    progressBar.hide()
                }
            }

            override fun onFailure(call: Call<LoginResponse>, t: Throwable) {
                progressBar.hide()
                showToast("Please try again!")
            }
        })
    }
}