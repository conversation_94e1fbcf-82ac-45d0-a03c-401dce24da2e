package com.basindev.afrothentiktv

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import androidx.fragment.app.Fragment
import com.basindev.afrothentiktv.extensions.getDeviceIpAddress
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.show
import com.bumptech.glide.Glide
import com.google.firebase.firestore.FirebaseFirestore

class MyAccountFragment : Fragment() {

    private val db = FirebaseFirestore.getInstance()
    val LOGIN_PREF_KEY = "LoginPrefs"
    val entity_id = "entity_id"
    val user_id = "user_id"
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var webView: WebView
    private lateinit var loader: ImageView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_my_account, container, false)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        super.onViewCreated(view, savedInstanceState)
        webView = view.findViewById(R.id.webView)
        webView.canGoBack()
        loader = view.findViewById(R.id.loader)
        sharedPreferences =
            requireContext().getSharedPreferences(LOGIN_PREF_KEY, Context.MODE_PRIVATE)
        var entityId = sharedPreferences.getString(entity_id, "")
        var userId = sharedPreferences.getString(user_id, "")
        // Enable JavaScript
        val webSettings: WebSettings = webView.settings
        webSettings.javaScriptEnabled = true

        // Load the preloaded website
        showLoader()
        // Load the preloaded website
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideLoader()
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                hideLoader()
            }
        }
        //getUrlFromFirebase()
        val deviceId = Settings.Secure.getString(
            requireContext().contentResolver,
            Settings.Secure.ANDROID_ID
        )

        //   val url = "https://www.afrothentik.com/z_account/cart?device_id=${deviceId}&ip=${getDeviceIpAddress()}&entity_id=${entityId}&user_id=${userId}"
        val url =
            "https://www.afrothentik.com/z_account/my_account?device_id=${deviceId}&ip=${getDeviceIpAddress()}"

        webView.loadUrl(url)
    }

    private fun showLoader(){
        loader.show()
        Glide
            .with(requireContext())
            .load(R.raw.anim_logo)
            .centerCrop()
            .into(loader)
    }
    fun onBackPressed(): Boolean {
        return if (webView.canGoBack()) {
            webView.goBack()
            true
        } else {
            false
        }
    }

    private fun hideLoader(){
        loader.hide()
    }
}