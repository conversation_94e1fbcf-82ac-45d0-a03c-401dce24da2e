package com.basindev.afrothentiktv

import android.Manifest
import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.os.Bundle
import android.provider.ContactsContract
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.ProgressBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.SearchView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.basindev.afrothentiktv.adapter.ContactsAdapter
import com.basindev.afrothentiktv.data.ContactData
import com.basindev.afrothentiktv.data.RetrofitInstance
import com.basindev.afrothentiktv.extensions.getDeviceIpAddress
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.show
import com.basindev.afrothentiktv.extensions.showToast
import com.basindev.afrothentiktv.model.Contact
import com.basindev.afrothentiktv.model.EntityIdResponse
import com.basindev.afrothentiktv.model.SaveContactsRequest
import com.basindev.afrothentiktv.model.SaveContactsResponse
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class ImportStaffFragment : Fragment() {
    private lateinit var contactsRecyclerView: RecyclerView
    private lateinit var searchView: SearchView
    private lateinit var saveButton: Button
    private lateinit var closeButton: Button
    private lateinit var newButton: Button
    private lateinit var contactsAdapter: ContactsAdapter
    private val selectedContacts = mutableListOf<ContactData>()
    private val READ_CONTACTS_REQUEST_CODE = 100
    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    private lateinit var sharedPreferences: SharedPreferences
    val LOGIN_PREF_KEY = "LoginPrefs"
    var entity_id = "entity_id"
    var user_id = "user_id"
    var deviceId = ""
    private val requestReadContactsPermission = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // Permission granted, load contacts
            loadContacts()
        } else {
            // Permission denied, show a message
            Toast.makeText(requireContext(), "Permission denied", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_import_staff, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        contactsRecyclerView = view.findViewById(R.id.contactsRecyclerView)
        saveButton = view.findViewById(R.id.saveButton)
        closeButton = view.findViewById(R.id.closeButton)
        newButton = view.findViewById(R.id.newButton)
        webView = view.findViewById(R.id.webView)
        searchView = view.findViewById(R.id.searchView)
        searchView.isIconified = false
        val webSettings: WebSettings = webView.settings
        webSettings.javaScriptEnabled = true
        webView.webViewClient = WebViewClient()
        progressBar = view.findViewById(R.id.progressBar)
        sharedPreferences =
            requireContext().getSharedPreferences(LOGIN_PREF_KEY, Context.MODE_PRIVATE)
        val entityId = sharedPreferences.getString(entity_id, "")
        val userId = sharedPreferences.getString(user_id, "")
        // Check if permission is already granted
        if (entityId.isNullOrEmpty() && userId.isNullOrEmpty()) {
            findNavController().navigate(R.id.action_importContactsFragment_to_loginFragment)
        } else {
            getOrLoadContacts()
        }
        // getEntityId()
        deviceId = Settings.Secure.getString(
            requireContext().contentResolver,
            Settings.Secure.ANDROID_ID
        )
        saveButton.setOnClickListener {
            // Handle saving selected contacts
            saveSelectedContacts()
        }


        searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                // When the search is submitted
                query?.let {
                    if (::contactsAdapter.isInitialized) {
                        contactsAdapter.filter(it)
                    }
                }
                return true
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                // While the user is typing
                newText?.let {
                    if (::contactsAdapter.isInitialized) {
                        contactsAdapter.filter(it)
                    }
                }
                return true
            }
        })
        closeButton.setOnClickListener {
            findNavController().popBackStack()
        }

        newButton.setOnClickListener {
            val nav =
                ImportContactsFragmentDirections.actionImportContactsFragmentToNewContactFragment("Contacts")
            findNavController().navigate(nav)
        }
    }

    private fun loadContacts() {
        // Load contacts from the phone using ContentResolver
        contactsRecyclerView.show()
        saveButton.show()
        webView.hide()

        val contactsList = mutableListOf<ContactData>()
        val uniqueNumbers = mutableSetOf<String>() // Set to track unique phone numbers

        val contentResolver = requireContext().contentResolver
        val cursor = contentResolver.query(
            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
            null, null, null, null
        )

        cursor?.use {
            val nameIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
            val phoneIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)

            while (it.moveToNext()) {
                val name = it.getString(nameIndex)
                val phone = it.getString(phoneIndex).replace("\\s".toRegex(), "") // Remove spaces

                // Check if the phone number is already in the set
                if (uniqueNumbers.add(phone)) {
                    // If it's not a duplicate, add to the contact list
                    contactsList.add(ContactData(contactName = name, contactPhone = phone))
                }
            }
        }
        contactsAdapter = ContactsAdapter(contactsList) { position, isSelected ->
            contactsList[position].isSelected = isSelected
            enableDisableButton(contactsList)
        }
        contactsRecyclerView.layoutManager = LinearLayoutManager(context)
        contactsRecyclerView.adapter = contactsAdapter
        // enableDisableButton()
    }

    private fun enableDisableButton(contactsList: List<ContactData>) {
        saveButton.isEnabled = isSelectedFromList(contactsList)
        if (isSelectedFromList(contactsList)) {
            saveButton.backgroundTintList =
                ContextCompat.getColorStateList(requireContext(), R.color.green)
        } else {
            saveButton.backgroundTintList =
                ContextCompat.getColorStateList(requireContext(), R.color.grey_D1D5DB)
        }
    }

    private fun isSelectedFromList(contactsList: List<ContactData>): Boolean {
        val selected = contactsList.filter { it.isSelected }
        if (selected.isEmpty()) {
            return false
        }
        return true
    }

    private fun saveSelectedContacts() {
        // Collect selected contacts
        val selected = contactsAdapter.contacts.filter { it.isSelected }
        val contactObj = selected.map {
            Contact(it.contactName, it.contactPhone)
        }
        val entityId = sharedPreferences.getString(entity_id, "") ?: ""
        val userId = sharedPreferences.getString(user_id, "") ?: ""
        saveContacts(SaveContactsRequest(entityId, userId, contactObj))
    }


    private fun saveContacts(savedContactRequest: SaveContactsRequest) {
        // Use the ApiService to send the POST request
        val call = RetrofitInstance.apiService.saveStaff(savedContactRequest)
        progressBar.show()
        call.enqueue(object : retrofit2.Callback<SaveContactsResponse> {
            override fun onResponse(
                call: Call<SaveContactsResponse>,
                response: Response<SaveContactsResponse>
            ) {
                if (response.isSuccessful) {
                    progressBar.hide()
                    // Handle successful response
                    val responseData = response.body()
                    showToast("Contacts saved successfully!")
                } else {
                    progressBar.hide()
                    // Handle error response
                    showToast("Error: ${response.errorBody()?.string()}")
                }
            }

            override fun onFailure(call: Call<SaveContactsResponse>, t: Throwable) {
                // Handle request failure
                progressBar.hide()
                showToast("Failure: ${t.message}")
            }
        })
    }

    private fun getOrLoadContacts() {
        if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.READ_CONTACTS)
            != PackageManager.PERMISSION_GRANTED
        ) {
            // Request permission
            /*  ActivityCompat.requestPermissions(
                  requireActivity(),
                  arrayOf(Manifest.permission.READ_CONTACTS),
                  READ_CONTACTS_REQUEST_CODE
              )*/
            requestReadContactsPermission.launch(Manifest.permission.READ_CONTACTS)
        } else {
            // Load contacts if permission is already granted
            loadContacts()
        }
    }

    private fun getEntityId() {
        progressBar.show()
        RetrofitInstance.apiService.getEntity().enqueue(object : Callback<EntityIdResponse> {
            override fun onResponse(
                call: Call<EntityIdResponse>,
                response: Response<EntityIdResponse>
            ) {
                if (response.isSuccessful && response.body() != null) {
                    progressBar.hide()
                    val enId = response.body()?.result?.entity_id
                    val usId = response.body()?.result?.user_id
                    if (!enId.isNullOrEmpty() && !usId.isNullOrEmpty()) {
                        contactsRecyclerView.show()
                        saveButton.show()
                        webView.hide()
                        entity_id = enId
                        user_id = usId
                        getOrLoadContacts()
                    } else {
                        contactsRecyclerView.hide()
                        saveButton.hide()
                        webView.show()
                        val url =
                            "https://www.afrothentik.com/my-account-2?device_id=${deviceId}&ip=${getDeviceIpAddress()}"
                        webView.loadUrl(url)
                    }
                } else {
                    contactsRecyclerView.hide()
                    saveButton.hide()
                    webView.show()
                    val url =
                        "https://www.afrothentik.com/my-account-2?device_id=${deviceId}&ip=${getDeviceIpAddress()}"
                    webView.loadUrl(url)
                }
            }

            override fun onFailure(call: Call<EntityIdResponse>, t: Throwable) {
                progressBar.hide()
                showToast("Please try again!")
            }
        })
    }

    /*    // Handle the result of permission request
        override fun onRequestPermissionsResult(
            requestCode: Int,
            permissions: Array<out String>,
            grantResults: IntArray
        ) {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults)
            if (requestCode == READ_CONTACTS_REQUEST_CODE &&
                grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
            ) {
                // Permission granted, load contacts
                loadContacts()
            } else {
                // Permission denied, show a message
                Toast.makeText(requireContext(), "Permission denied", Toast.LENGTH_SHORT).show()
            }
        }*/

}
