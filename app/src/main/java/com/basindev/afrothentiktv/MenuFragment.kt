package com.basindev.afrothentiktv

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.basindev.afrothentiktv.extensions.hide
import com.basindev.afrothentiktv.extensions.show

class MenuFragment : Fragment() {
    val LOGIN_PREF_KEY = "LoginPrefs"
    val entity_id = "entity_id"
    val user_id = "user_id"
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var toolbarTitle: TextView
    private lateinit var importContacts: TextView
    private lateinit var newContacts: TextView
    private lateinit var close: TextView
    private lateinit var logoutButton: TextView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_menu, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        sharedPreferences =
            requireContext().getSharedPreferences(LOGIN_PREF_KEY, Context.MODE_PRIVATE)
        val toolbarLayout: View = view.findViewById(R.id.layoutToolbar)
        toolbarTitle = toolbarLayout.findViewById(R.id.toolbar_title)
        toolbarTitle.text = "Menu"
        importContacts = view.findViewById(R.id.importContacts)
        newContacts = view.findViewById(R.id.newContacts)
        close = view.findViewById(R.id.close)
        logoutButton = view.findViewById(R.id.logoutButton)
        var entityId = sharedPreferences.getString(entity_id, "")
        var userId = sharedPreferences.getString(user_id, "")
        if (entityId.isNullOrEmpty() && userId.isNullOrEmpty()) {
            logoutButton.hide()
        } else {
            logoutButton.show()
        }
        importContacts.setOnClickListener {
            /*     if (entityId.isNullOrEmpty() && userId.isNullOrEmpty()) {
                     findNavController().navigate(R.id.loginFragment)
                 } else {*/
            findNavController().navigate(R.id.importContactsFragment)
            //   }
        }
        newContacts.setOnClickListener {
            /*      if (entityId.isNullOrEmpty() && userId.isNullOrEmpty()) {
                      findNavController().navigate(R.id.loginFragment)
                  } else {*/
            findNavController().navigate(R.id.newContactFragment)
            //  }
        }
        close.setOnClickListener {
            findNavController().popBackStack()
        }

        logoutButton.setOnClickListener {
            sharedPreferences.edit().clear().apply()
            logoutButton.hide()
            entityId = sharedPreferences.getString(entity_id, "")
            userId = sharedPreferences.getString(user_id, "")
        }
    }

}