package com.basindev.afrothentiktv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.basindev.afrothentiktv.R
import com.basindev.afrothentiktv.model.BottomMenuItem

class HorizontalAdapter(
    private val items: List<BottomMenuItem>,
    private val itemClick: (BottomMenuItem) -> Unit
) :
    RecyclerView.Adapter<HorizontalAdapter.ViewHolder>() {

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val image: ImageView = itemView.findViewById(R.id.itemImage)
        val text: TextView = itemView.findViewById(R.id.itemText)
        val parent: LinearLayout = itemView.findViewById(R.id.parent)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.menu_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.image.setImageResource(item.ImageId)
        holder.text.text = item.text
        holder.parent.setOnClickListener {
            itemClick(item)
        }
    }

    override fun getItemCount(): Int = items.size
}
