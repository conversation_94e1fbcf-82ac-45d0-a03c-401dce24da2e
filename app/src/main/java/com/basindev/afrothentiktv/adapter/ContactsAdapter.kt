package com.basindev.afrothentiktv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.basindev.afrothentiktv.R
import com.basindev.afrothentiktv.data.ContactData

// ContactsAdapter.kt
class ContactsAdapter(
    val contacts: List<ContactData>,
    private val onItemChecked: (Int, Boolean) -> Unit
) : RecyclerView.Adapter<ContactsAdapter.ContactViewHolder>() {

    private var filteredContacts: List<ContactData> = contacts.toList()
    var contactsSelected = MutableList(contacts.size) { false }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ContactViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_contact, parent, false)
        return ContactViewHolder(view)
    }

  /*  override fun onBindViewHolder(holder: ContactViewHolder, position: Int) {
        val contact = filteredContacts[position]
        holder.bind(contact)
        holder.itemView.findViewById<CheckBox>(R.id.contactCheckbox).apply {
            isChecked = contact.isSelected
            setOnCheckedChangeListener { _, isChecked ->
                contactsSelected[position] = isChecked
                onItemChecked(position, isChecked)
            }
        }
    }*/

    override fun onBindViewHolder(holder: ContactViewHolder, position: Int) {
        val contact = filteredContacts[position]
        holder.bind(contact)

        val checkbox = holder.itemView.findViewById<CheckBox>(R.id.contactCheckbox)
        checkbox.setOnCheckedChangeListener(null) // Prevent unwanted triggers when reusing views
        checkbox.isChecked = contact.isSelected

        checkbox.setOnCheckedChangeListener { _, isChecked ->
            contact.isSelected = isChecked
            onItemChecked(position, isChecked)
        }
    }

    fun filter(query: String) {
        filteredContacts = if (query.isEmpty()) {
            contacts
        } else {
            contacts.filter {
                it.contactName.contains(query, ignoreCase = true) ||
                        it.contactPhone.contains(query, ignoreCase = true)
            }
        }
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = filteredContacts.size

    class ContactViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val contactNameTextView: TextView = itemView.findViewById(R.id.contactName)
        private val contactPhoneTextView: TextView = itemView.findViewById(R.id.contactPhone)

        fun bind(contact: ContactData) {
            contactNameTextView.text = contact.contactName
            contactPhoneTextView.text = contact.contactPhone
        }
    }
}
