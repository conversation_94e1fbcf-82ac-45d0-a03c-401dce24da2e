package com.basindev.afrothentiktv.extensions

import android.util.Patterns
import android.view.View
import android.widget.Toast
import androidx.fragment.app.Fragment
import java.net.InetAddress
import java.net.NetworkInterface

fun isEmailInTextFieldValid(email: String?): Boolean {
    email?.let {
        return Patterns.EMAIL_ADDRESS.matcher(email)
            .matches()
    }
    return false
}

fun Fragment.showToast(message: String) {
    Toast.makeText(this.context, message, Toast.LENGTH_SHORT).show()
}

fun View.show() = View.VISIBLE.also { this.visibility = it }
fun View.hide() = View.GONE.also { this.visibility = it }
fun View.invisible() = View.INVISIBLE.also { this.visibility = it }

fun getDeviceIpAddress(): String {
    try {
        val interfaces = NetworkInterface.getNetworkInterfaces()
        while (interfaces.hasMoreElements()) {
            val networkInterface = interfaces.nextElement()
            val addresses = networkInterface.inetAddresses
            while (addresses.hasMoreElements()) {
                val inetAddress = addresses.nextElement()
                if (!inetAddress.isLoopbackAddress && inetAddress is InetAddress) {
                    return inetAddress.hostAddress ?: "Unknown"
                }
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return "Unknown"
}
