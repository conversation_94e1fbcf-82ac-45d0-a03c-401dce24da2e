package com.basindev.afrothentiktv

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent


class HdmiReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        if ("android.intent.action.HDMI_PLUGGED" == intent.action) {
            val connected = intent.getBooleanExtra("state", false)
            if (connected) {
                // HDMI is connected, launch the app
                val launchIntent = Intent(context, MainActivity::class.java)
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(launchIntent)
            }
        }
    }
}
